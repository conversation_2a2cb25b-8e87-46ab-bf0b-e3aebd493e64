/// -----
/// WebViewScreen
///
/// 通用的WebView页面组件，支持加载网络URL和本地HTML文件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// WebView页面组件
///
/// 支持两种加载方式：
/// 1. 加载网络URL - 通过url参数传入
/// 2. 加载本地HTML文件 - 通过assetPath参数传入
class WebViewScreen extends StatefulWidget {
  /// 页面标题
  final String title;

  /// 本地HTML文件路径（可选）
  final String? assetPath;

  /// 网络URL（可选）
  final String? url;

  /// 构造函数 - 加载本地HTML文件
  const WebViewScreen.asset({
    required this.title,
    required this.assetPath,
    Key? key,
  }) : url = null,
       super(key: key);

  /// 构造函数 - 加载网络URL
  const WebViewScreen.url({
    required this.title,
    required this.url,
    Key? key,
  }) : assetPath = null,
       super(key: key);

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initWebViewController();
  }

  Future<void> _initWebViewController() async {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {},
        ),
      );

    // 根据参数类型加载内容
    if (widget.url != null) {
      // 加载网络URL
      await _controller.loadRequest(Uri.parse(widget.url!));
    } else if (widget.assetPath != null) {
      // 从assets加载HTML文件
      final String htmlContent = await rootBundle.loadString(widget.assetPath!);
      await _controller.loadHtmlString(htmlContent);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: widget.title),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
